/**
 * Business Analysis Agent Manager
 * 
 * Manages the Business Analysis Agent system and integrates with PMO workflows.
 * Provides high-level interface for conducting business analysis and managing results.
 */

import { AgentMemoryManager } from '../AgentMemoryManager';
import { LlmProvider } from '../../tools/llm-tool';
import { processWithGroq } from '../../tools/groq-ai';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { v4 as uuidv4 } from 'uuid';

export interface BusinessAnalysisAgentManagerConfig {
  userId: string;
  defaultLlmProvider?: LlmProvider;
  defaultLlmModel?: string;
}

export interface BusinessAnalysisPreview {
  analysisType: BusinessAnalysisType;
  selectedAnalysts: BusinessAnalyst[];
  estimatedDuration: string;
  expectedOutputs: string[];
  modelConfiguration: {
    primaryModel: string;
    fallbackModel: string;
    validationModel?: string;
  };
}

export interface PMOBusinessAnalysisRequest {
  pmoId: string;
  title: string;
  description: string;
  analysisType: BusinessAnalysisType;
  selectedAnalystIds?: string[];
  primaryModel?: string;
  fallbackModel?: string;
  validationModel?: string;
  userId: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  // Enhanced analysis capabilities
  enableSystemAnalysis?: boolean;
  enableRequirementsEngineering?: boolean;
  enableSpecificationDevelopment?: boolean;
  documentCategory?: string;
}

export enum BusinessAnalysisType {
  SYSTEM_ANALYSIS = 'system-analysis',
  PRODUCT_OVERVIEW = 'product-overview', 
  REQUIREMENTS_ENGINEERING = 'requirements-engineering',
  SPECIFICATION_DEVELOPMENT = 'specification-development',
  COMPREHENSIVE = 'comprehensive'
}

export interface BusinessAnalyst {
  id: string;
  name: string;
  specialty: string;
  description: string;
  capabilities: string[];
  preferredModel: string;
  preferredProvider: LlmProvider;
}

export interface BusinessAnalysisRequest {
  prompt: string;
  context?: string;
  analysisType: BusinessAnalysisType;
  selectedAnalysts: string[];
  primaryModel?: string;
  fallbackModel?: string;
  validationModel?: string;
  userId: string;
  pmoId?: string;
  // Enhanced analysis capabilities
  enableSystemAnalysis?: boolean;
  enableRequirementsEngineering?: boolean;
  enableSpecificationDevelopment?: boolean;
  documentCategory?: string;
}

export interface BusinessAnalysisResult {
  analysisId: string;
  originalPrompt: string;
  optimizedPrompt: string;
  analysisType: BusinessAnalysisType;
  criteria: string;
  criteriaModel: string;
  criteriaProvider: LlmProvider;
  optimizationModel: string;
  optimizationProvider: LlmProvider;
  analystResponses: AnalystResponse[];
  consolidatedAnalysis: string;
  validationModel?: string;
  validationProvider?: LlmProvider;
  consolidatedReport?: string;
  timestamp: Date;
  duration: number;
  metadata: {
    pmoId?: string;
    userId: string;
    priority?: string;
    teamId: AgenticTeamId;
    modelConfiguration: {
      primaryModel: string;
      fallbackModel: string;
      validationModel?: string;
    }
  };
}

export interface AnalystResponse {
  analystId: string;
  analystName: string;
  specialty: string;
  response: string;
  model: string;
  provider: LlmProvider;
  timestamp: Date;
  metadata: {
    analysisType: BusinessAnalysisType;
    wordCount: number;
    keyFindings: string[];
  };
}

/**
 * Business Analysis Agent Manager
 * Coordinates multiple business analysts to provide comprehensive analysis
 */
export class BusinessAnalysisAgentManager {
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private defaultLlmProvider: LlmProvider;
  private defaultLlmModel: string;

  constructor(config: BusinessAnalysisAgentManagerConfig) {
    this.userId = config.userId;
    this.defaultLlmProvider = config.defaultLlmProvider || 'anthropic';
    this.defaultLlmModel = config.defaultLlmModel || 'claude-sonnet-4-0';
    this.memoryManager = new AgentMemoryManager(config.userId);
  }

  /**
   * Get available business analysts
   */
  getAvailableAnalysts(): BusinessAnalyst[] {
    return [
      {
        id: 'system-analyst',
        name: 'System Analyst',
        specialty: 'System Architecture & Component Analysis',
        description: 'Specializes in comprehensive system architecture analysis, component identification, and technical system documentation.',
        capabilities: [
          'System architecture analysis',
          'Component identification and mapping',
          'Technical system documentation',
          'Integration point analysis',
          'Performance and scalability assessment'
        ],
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic'
      },
      {
        id: 'product-analyst',
        name: 'Product Analyst', 
        specialty: 'Product Overview & Strategic Positioning',
        description: 'Focuses on high-level product vision, strategic positioning, and market analysis.',
        capabilities: [
          'Product vision development',
          'Strategic positioning analysis',
          'Market opportunity assessment',
          'Competitive analysis',
          'Product roadmap planning'
        ],
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic'
      },
      {
        id: 'requirements-engineer',
        name: 'Requirements Engineer',
        specialty: 'Requirements Engineering & Documentation',
        description: 'Expert in requirements gathering, analysis, and comprehensive documentation.',
        capabilities: [
          'High-level requirements gathering',
          'Detailed functional requirements specification',
          'Non-functional requirements analysis',
          'Requirements traceability',
          'Stakeholder requirements analysis'
        ],
        preferredModel: 'claude-sonnet-4-0',
        preferredProvider: 'anthropic'
      },
      {
        id: 'specification-developer',
        name: 'Specification Developer',
        specialty: 'Functional Specifications & Use Case Development',
        description: 'Specializes in detailed functional specifications, use case modeling, and test case generation.',
        capabilities: [
          'Functional specifications development',
          'Product use case analysis and modeling',
          'User story creation with acceptance criteria',
          'Test case generation and validation scenarios',
          'Technical specification documentation'
        ],
        preferredModel: 'deepseek-r1-distill-llama-70b',
        preferredProvider: 'groq'
      }
    ];
  }

  /**
   * Get recommended analysts for a specific analysis type
   */
  getRecommendedAnalysts(analysisType: BusinessAnalysisType): BusinessAnalyst[] {
    const allAnalysts = this.getAvailableAnalysts();
    
    switch (analysisType) {
      case BusinessAnalysisType.SYSTEM_ANALYSIS:
        return allAnalysts.filter(a => a.id === 'system-analyst');
      case BusinessAnalysisType.PRODUCT_OVERVIEW:
        return allAnalysts.filter(a => a.id === 'product-analyst');
      case BusinessAnalysisType.REQUIREMENTS_ENGINEERING:
        return allAnalysts.filter(a => a.id === 'requirements-engineer');
      case BusinessAnalysisType.SPECIFICATION_DEVELOPMENT:
        return allAnalysts.filter(a => a.id === 'specification-developer');
      case BusinessAnalysisType.COMPREHENSIVE:
      default:
        return allAnalysts; // Use all analysts for comprehensive analysis
    }
  }

  /**
   * Get available analysis types
   */
  getAnalysisTypes(): { id: BusinessAnalysisType; name: string; description: string; }[] {
    return [
      {
        id: BusinessAnalysisType.SYSTEM_ANALYSIS,
        name: 'System Analysis',
        description: 'Comprehensive system architecture and component analysis'
      },
      {
        id: BusinessAnalysisType.PRODUCT_OVERVIEW,
        name: 'Product Overview',
        description: 'High-level product vision and strategic positioning'
      },
      {
        id: BusinessAnalysisType.REQUIREMENTS_ENGINEERING,
        name: 'Requirements Engineering',
        description: 'Detailed requirements gathering and documentation'
      },
      {
        id: BusinessAnalysisType.SPECIFICATION_DEVELOPMENT,
        name: 'Specification Development',
        description: 'Functional specifications and use case development'
      },
      {
        id: BusinessAnalysisType.COMPREHENSIVE,
        name: 'Comprehensive Analysis',
        description: 'Full business analysis across all domains'
      }
    ];
  }

  /**
   * Get intelligent analyst recommendations using LLM
   */
  async getIntelligentAnalystRecommendations(
    title: string,
    description: string,
    analysisType?: BusinessAnalysisType,
    maxAnalysts: number = 3
  ): Promise<BusinessAnalyst[]> {
    const availableAnalysts = this.getAvailableAnalysts();
    
    const selectionPrompt = `
You are an expert Business Analysis coordinator tasked with selecting the most appropriate analysts for a business analysis request.

**Request Details:**
Title: ${title}
Description: ${description}
Analysis Type: ${analysisType || 'Not specified'}

**Available Analysts:**
${availableAnalysts.map(analyst => `
- ${analyst.id}: ${analyst.name} (${analyst.specialty})
  Capabilities: ${analyst.capabilities.join(', ')}
`).join('')}

**Selection Criteria:**
- Select ${maxAnalysts} most relevant analysts
- Consider the request content and analysis type
- Ensure complementary expertise coverage
- Prioritize analysts whose capabilities best match the request

Respond with ONLY a JSON array of analyst IDs in order of relevance:
["analyst-id-1", "analyst-id-2", "analyst-id-3"]

Do not include any explanation or additional text - only the JSON array.
`;

    try {
      // Use Groq DeepSeek to intelligently select analysts
      const response = await processWithGroq({
        prompt: selectionPrompt,
        model: "deepseek-r1-distill-llama-70b-distill-llama-70b",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 1000
        }
      });

      const selectedIds = JSON.parse(response.trim());
      return selectedIds
        .map((id: string) => availableAnalysts.find(a => a.id === id))
        .filter((analyst: BusinessAnalyst | undefined): analyst is BusinessAnalyst => analyst !== undefined)
        .slice(0, maxAnalysts);

    } catch (error) {
      console.error('Error in intelligent analyst selection:', error);
      // Fallback to type-based selection
      return this.getRecommendedAnalysts(analysisType || BusinessAnalysisType.COMPREHENSIVE).slice(0, maxAnalysts);
    }
  }

  /**
   * Generate analysis preview for PMO integration
   */
  async generateAnalysisPreview(request: Partial<PMOBusinessAnalysisRequest>): Promise<BusinessAnalysisPreview> {
    const analysisType = request.analysisType || BusinessAnalysisType.COMPREHENSIVE;
    const selectedAnalysts = request.selectedAnalystIds
      ? request.selectedAnalystIds.map(id => this.getAvailableAnalysts().find(a => a.id === id)).filter(Boolean) as BusinessAnalyst[]
      : this.getRecommendedAnalysts(analysisType);

    const estimatedDuration = this.calculateEstimatedDuration(selectedAnalysts.length, analysisType);
    const expectedOutputs = this.getExpectedOutputs(analysisType);

    return {
      analysisType,
      selectedAnalysts,
      estimatedDuration,
      expectedOutputs,
      modelConfiguration: {
        primaryModel: request.primaryModel || 'claude-sonnet-4-0',
        fallbackModel: request.fallbackModel || 'deepseek-r1-distill-llama-70b',
        validationModel: request.validationModel
      }
    };
  }

  /**
   * Conduct PMO business analysis investigation
   */
  async conductPMOBusinessAnalysis(
    request: PMOBusinessAnalysisRequest,
    progressCallback?: (stage: string, progress: number, message: string) => void
  ): Promise<BusinessAnalysisResult> {
    console.log(`Starting PMO business analysis for ${request.pmoId}: ${request.title}`);

    try {
      // Initialize
      progressCallback?.('initializing', 10, 'Initializing business analysis workflow');

      // Convert PMO request to analysis request
      const analysisRequest: BusinessAnalysisRequest = {
        prompt: request.title,
        context: request.description,
        analysisType: request.analysisType,
        selectedAnalysts: request.selectedAnalystIds || [],
        primaryModel: request.primaryModel,
        fallbackModel: request.fallbackModel,
        validationModel: request.validationModel,
        userId: request.userId,
        pmoId: request.pmoId,
        enableSystemAnalysis: request.enableSystemAnalysis !== false,
        enableRequirementsEngineering: request.enableRequirementsEngineering !== false,
        enableSpecificationDevelopment: request.enableSpecificationDevelopment !== false,
        documentCategory: request.documentCategory
      };

      // Conduct the actual analysis
      progressCallback?.('analyzing', 50, 'Conducting business analysis');
      const result = await this.conductBusinessAnalysis(analysisRequest);

      // Complete
      progressCallback?.('complete', 100, 'Business analysis completed successfully');

      // Store PMO-specific metadata
      await this.storePMOAnalysisResult(result, request);

      // Store analysis result in Firebase Agent_Output collection for PMO Output tab
      await this.storePMOAgentOutput(result, request);

      return result;

    } catch (error) {
      console.error(`PMO business analysis failed for ${request.pmoId}:`, error);
      throw error;
    }
  }

  /**
   * Core business analysis method
   */
  private async conductBusinessAnalysis(request: BusinessAnalysisRequest): Promise<BusinessAnalysisResult> {
    const analysisId = `ba_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    console.log(`Conducting business analysis ${analysisId} for: ${request.prompt}`);

    try {
      // Step 1: Generate analysis criteria
      const criteria = await this.generateAnalysisCriteria(request);

      // Step 2: Optimize the analysis prompt
      const optimizedPrompt = await this.optimizeAnalysisPrompt(request);

      // Step 3: Conduct analysis with selected analysts
      const analystResponses = await this.conductAnalystAnalyses(request, optimizedPrompt);

      // Step 4: Consolidate analyst findings
      const consolidatedAnalysis = await this.consolidateAnalystFindings(request, criteria, analystResponses);

      // Step 5: Optional validation
      let consolidatedReport: string | undefined = undefined;
      if (request.validationModel) {
        consolidatedReport = await this.validateAndConsolidate(request, criteria, consolidatedAnalysis, analystResponses);
      }

      const duration = Date.now() - startTime;

      const result: BusinessAnalysisResult = {
        analysisId,
        originalPrompt: request.prompt,
        optimizedPrompt,
        analysisType: request.analysisType,
        criteria,
        criteriaModel: request.primaryModel || 'claude-sonnet-4-0',
        criteriaProvider: 'anthropic',
        optimizationModel: request.fallbackModel || 'deepseek-r1-distill-llama-70b',
        optimizationProvider: 'groq',
        analystResponses,
        consolidatedAnalysis,
        validationModel: request.validationModel,
        validationProvider: request.validationModel ? 'anthropic' : undefined,
        consolidatedReport,
        timestamp: new Date(),
        duration,
        metadata: {
          pmoId: request.pmoId,
          userId: request.userId,
          teamId: AgenticTeamId.BusinessAnalysis,
          modelConfiguration: {
            primaryModel: request.primaryModel || 'claude-sonnet-4-0',
            fallbackModel: request.fallbackModel || 'deepseek-r1-distill-llama-70b',
            validationModel: request.validationModel
          }
        }
      };

      console.log(`Business analysis ${analysisId} completed in ${duration}ms`);
      return result;

    } catch (error) {
      console.error(`Business analysis ${analysisId} failed:`, error);
      throw error;
    }
  }

  /**
   * Calculate estimated duration based on analysts and analysis type
   */
  private calculateEstimatedDuration(analystCount: number, analysisType: BusinessAnalysisType): string {
    const baseMinutes = analysisType === BusinessAnalysisType.COMPREHENSIVE ? 15 : 8;
    const totalMinutes = baseMinutes + (analystCount * 3);

    if (totalMinutes < 60) {
      return `${totalMinutes} minutes`;
    } else {
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours} hour${hours > 1 ? 's' : ''}`;
    }
  }

  /**
   * Get expected outputs for analysis type
   */
  private getExpectedOutputs(analysisType: BusinessAnalysisType): string[] {
    const baseOutputs = [
      'Comprehensive business analysis report',
      'Executive summary with key findings',
      'Actionable recommendations'
    ];

    switch (analysisType) {
      case BusinessAnalysisType.SYSTEM_ANALYSIS:
        return [
          ...baseOutputs,
          'System architecture documentation',
          'Component analysis and mapping',
          'Technical integration assessment'
        ];
      case BusinessAnalysisType.PRODUCT_OVERVIEW:
        return [
          ...baseOutputs,
          'Product vision statement',
          'Strategic positioning analysis',
          'Market opportunity assessment'
        ];
      case BusinessAnalysisType.REQUIREMENTS_ENGINEERING:
        return [
          ...baseOutputs,
          'Functional requirements specification',
          'Non-functional requirements analysis',
          'Requirements traceability matrix'
        ];
      case BusinessAnalysisType.SPECIFICATION_DEVELOPMENT:
        return [
          ...baseOutputs,
          'Detailed functional specifications',
          'Use case models and scenarios',
          'Test case generation and validation'
        ];
      case BusinessAnalysisType.COMPREHENSIVE:
      default:
        return [
          ...baseOutputs,
          'Multi-domain analysis across all areas',
          'Cross-functional recommendations',
          'Strategic implementation roadmap'
        ];
    }
  }

  /**
   * Generate analysis criteria using primary model
   */
  private async generateAnalysisCriteria(request: BusinessAnalysisRequest): Promise<string> {
    const criteriaPrompt = `
You are an expert Business Analysis coordinator. Generate comprehensive analysis criteria for the following business analysis request.

**Analysis Request:**
Type: ${request.analysisType}
Prompt: ${request.prompt}
Context: ${request.context || 'No additional context provided'}

**Analysis Capabilities:**
- System Analysis: ${request.enableSystemAnalysis ? 'Enabled' : 'Disabled'}
- Requirements Engineering: ${request.enableRequirementsEngineering ? 'Enabled' : 'Disabled'}
- Specification Development: ${request.enableSpecificationDevelopment ? 'Enabled' : 'Disabled'}

Generate specific, measurable criteria that business analysts should use to evaluate this request. Include:

1. **Primary Analysis Objectives** - What are the main goals?
2. **Key Evaluation Areas** - What specific areas should be analyzed?
3. **Quality Standards** - What constitutes a thorough analysis?
4. **Deliverable Requirements** - What outputs are expected?
5. **Success Metrics** - How will analysis quality be measured?

Provide detailed, actionable criteria that will guide multiple analysts to produce consistent, high-quality results.
`;

    try {
      const { llmTool } = await import('../../tools/llm-tool');
      const response = await llmTool.processContent({
        prompt: criteriaPrompt,
        provider: 'anthropic',
        model: request.primaryModel || 'claude-sonnet-4-0',
        modelOptions: {
          temperature: 0.3,
          maxTokens: 4000
        }
      });

      return response;
    } catch (error) {
      console.error('Error generating analysis criteria:', error);
      return `Standard business analysis criteria for ${request.analysisType} analysis.`;
    }
  }

  /**
   * Optimize analysis prompt using fallback model
   */
  private async optimizeAnalysisPrompt(request: BusinessAnalysisRequest): Promise<string> {
    const optimizationPrompt = `
You are an expert prompt engineer specializing in business analysis. Optimize the following analysis request for maximum clarity and effectiveness.

**Original Request:**
"${request.prompt}"

**Context:**
${request.context || 'No additional context'}

**Analysis Type:** ${request.analysisType}

**Optimization Goals:**
1. Enhance clarity and specificity
2. Add relevant business analysis frameworks
3. Include industry best practices
4. Ensure comprehensive coverage
5. Make actionable and measurable

Transform this into an optimized prompt that will guide business analysts to produce exceptional results. The optimized prompt should be clear, comprehensive, and professionally structured.

Return only the optimized prompt without additional commentary.
`;

    try {
      const response = await processWithGroq({
        prompt: optimizationPrompt,
        model: request.fallbackModel || "deepseek-r1-distill-llama-70b-distill-llama-70b",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 4500
        }
      });

      return response.trim();
    } catch (error) {
      console.error('Error optimizing analysis prompt:', error);
      return request.prompt; // Fallback to original prompt
    }
  }

  /**
   * Conduct analyses with selected analysts
   */
  private async conductAnalystAnalyses(
    request: BusinessAnalysisRequest,
    optimizedPrompt: string
  ): Promise<AnalystResponse[]> {
    const availableAnalysts = this.getAvailableAnalysts();
    const selectedAnalysts = request.selectedAnalysts.length > 0
      ? request.selectedAnalysts.map(id => availableAnalysts.find(a => a.id === id)).filter(Boolean) as BusinessAnalyst[]
      : this.getRecommendedAnalysts(request.analysisType);

    console.log(`Conducting analysis with ${selectedAnalysts.length} analysts`);

    const analystPromises = selectedAnalysts.map(async (analyst) => {
      return this.conductSingleAnalystAnalysis(analyst, optimizedPrompt, request);
    });

    const responses = await Promise.all(analystPromises);
    return responses.filter(response => response !== null) as AnalystResponse[];
  }

  /**
   * Conduct analysis with a single analyst
   */
  private async conductSingleAnalystAnalysis(
    analyst: BusinessAnalyst,
    optimizedPrompt: string,
    request: BusinessAnalysisRequest
  ): Promise<AnalystResponse | null> {
    try {
      console.log(`${analyst.name} conducting ${request.analysisType} analysis`);

      // Build context for the analyst
      let analysisContext = '';

      // Add document analysis if enabled
      if (request.enableSystemAnalysis || request.enableRequirementsEngineering || request.enableSpecificationDevelopment) {
        // Query internal documents for analysis context
        try {
          const { QueryDocumentsAgent } = await import('../../../components/Agents/QueryDocumentsAgent');
          const queryAgent = new QueryDocumentsAgent();

          const documentResults = await queryAgent.process({
            query: `${optimizedPrompt} ${request.analysisType} business analysis`,
            userId: request.userId,
            category: request.documentCategory,
            useInternetSearch: false,
            model: analyst.preferredModel
          });

          if (documentResults.success && documentResults.content) {
            analysisContext += `\n**INTERNAL DOCUMENT ANALYSIS:**\n${documentResults.content}\n`;
          }
        } catch (error) {
          console.warn(`Document analysis failed for ${analyst.name}:`, error);
        }
      }

      // Create specialized prompt for this analyst
      const specializedPrompt = `
You are ${analyst.name}, a ${analyst.specialty} specialist. You have been assigned to conduct a comprehensive business analysis.

**Your Expertise:**
${analyst.capabilities.map(cap => `- ${cap}`).join('\n')}

**Analysis Request:**
${optimizedPrompt}

**Analysis Type:** ${request.analysisType}

**Additional Context:**
${analysisContext || 'No additional context available'}

**Your Task:**
Provide a thorough business analysis from your specialized perspective. Focus on your areas of expertise while ensuring comprehensive coverage. Include:

1. **Executive Summary** - Key findings and recommendations
2. **Detailed Analysis** - In-depth examination using your expertise
3. **Specific Recommendations** - Actionable next steps
4. **Risk Assessment** - Potential challenges and mitigation strategies
5. **Implementation Considerations** - Practical aspects of execution

Ensure your analysis is professional, detailed, and actionable. Draw upon your specialized knowledge to provide unique insights that complement other analysts' perspectives.
`;

      // Process with analyst's preferred model and provider
      const { llmTool } = await import('../../tools/llm-tool');
      const response = await llmTool.processContent({
        prompt: specializedPrompt,
        provider: analyst.preferredProvider,
        model: analyst.preferredModel,
        modelOptions: {
          temperature: 0.4,
          maxTokens: 4000
        }
      });

      // Extract key findings (simplified for now)
      const keyFindings = this.extractKeyFindings(response);

      return {
        analystId: analyst.id,
        analystName: analyst.name,
        specialty: analyst.specialty,
        response,
        model: analyst.preferredModel,
        provider: analyst.preferredProvider,
        timestamp: new Date(),
        metadata: {
          analysisType: request.analysisType,
          wordCount: response.split(' ').length,
          keyFindings
        }
      };

    } catch (error) {
      console.error(`Analysis failed for ${analyst.name}:`, error);
      return null;
    }
  }

  /**
   * Extract key findings from analyst response
   */
  private extractKeyFindings(response: string): string[] {
    // Simple extraction - look for bullet points, numbered lists, or key phrases
    const findings: string[] = [];

    const lines = response.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.match(/^[-*•]\s+/) || trimmed.match(/^\d+\.\s+/) || trimmed.includes('Key finding:') || trimmed.includes('Recommendation:')) {
        findings.push(trimmed.replace(/^[-*•]\s+/, '').replace(/^\d+\.\s+/, ''));
      }
    }

    return findings.slice(0, 5); // Limit to top 5 findings
  }

  /**
   * Consolidate analyst findings into unified analysis
   */
  private async consolidateAnalystFindings(
    request: BusinessAnalysisRequest,
    criteria: string,
    analystResponses: AnalystResponse[]
  ): Promise<string> {
    const consolidationPrompt = `
You are a Senior Business Analysis Director tasked with consolidating multiple analyst reports into a comprehensive, unified business analysis.

**Analysis Criteria:**
${criteria}

**Original Request:**
${request.prompt}

**Analysis Type:** ${request.analysisType}

**Analyst Reports:**
${analystResponses.map((response, index) => `
**${response.analystName} (${response.specialty}):**
${response.response}

**Key Findings:**
${response.metadata.keyFindings.map(finding => `- ${finding}`).join('\n')}
`).join('\n---\n')}

**Your Task:**
Synthesize these analyst reports into a comprehensive, executive-level business analysis. Ensure:

1. **Executive Summary** - High-level overview and key insights
2. **Consolidated Findings** - Unified view of all analyst insights
3. **Cross-Functional Analysis** - How different perspectives complement each other
4. **Strategic Recommendations** - Prioritized, actionable recommendations
5. **Implementation Roadmap** - Practical next steps and timeline
6. **Risk Assessment** - Comprehensive risk analysis and mitigation strategies

Maintain professional tone, eliminate redundancy, and highlight areas where analysts agree or disagree. Provide a cohesive narrative that leverages all specialist perspectives.
`;

    try {
      const { llmTool } = await import('../../tools/llm-tool');
      const response = await llmTool.processContent({
        prompt: consolidationPrompt,
        provider: 'anthropic',
        model: request.primaryModel || 'claude-sonnet-4-0',
        modelOptions: {
          temperature: 0.3,
          maxTokens: 6000
        }
      });

      return response;
    } catch (error) {
      console.error('Error consolidating analyst findings:', error);
      return 'Error occurred during consolidation. Please review individual analyst reports.';
    }
  }

  /**
   * Validate and create final consolidated report
   */
  private async validateAndConsolidate(
    request: BusinessAnalysisRequest,
    criteria: string,
    consolidatedAnalysis: string,
    analystResponses: AnalystResponse[]
  ): Promise<string> {
    const validationPrompt = `
You are a Quality Assurance Director for Business Analysis. Review and validate the consolidated business analysis report.

**Analysis Criteria:**
${criteria}

**Consolidated Analysis:**
${consolidatedAnalysis}

**Original Analyst Count:** ${analystResponses.length}
**Analysis Type:** ${request.analysisType}

**Validation Tasks:**
1. **Completeness Check** - Ensure all required elements are covered
2. **Quality Assessment** - Verify analysis depth and rigor
3. **Consistency Review** - Check for logical flow and coherence
4. **Recommendation Validation** - Assess feasibility and actionability
5. **Final Enhancement** - Add any missing critical elements

**Output Requirements:**
Provide a final, validated business analysis report that:
- Meets all quality standards
- Addresses any gaps or weaknesses
- Enhances clarity and actionability
- Maintains professional executive-level presentation

If the analysis is satisfactory, enhance it. If significant issues exist, provide a corrected version.
`;

    try {
      const { llmTool } = await import('../../tools/llm-tool');
      const response = await llmTool.processContent({
        prompt: validationPrompt,
        provider: 'anthropic',
        model: request.validationModel || 'claude-sonnet-4-0',
        modelOptions: {
          temperature: 0.2,
          maxTokens: 8000
        }
      });

      return response;
    } catch (error) {
      console.error('Error in validation and consolidation:', error);
      return consolidatedAnalysis; // Return original if validation fails
    }
  }

  /**
   * Store PMO analysis result in memory
   */
  private async storePMOAnalysisResult(result: BusinessAnalysisResult, request: PMOBusinessAnalysisRequest): Promise<void> {
    try {
      const agentMemory = {
        Agent_Response: {
          [`pmo_business_analysis_${result.analysisId}`]: {
            type: 'pmo-business-analysis',
            timestamp: new Date(),
            data: {
              analysisId: result.analysisId,
              pmoId: request.pmoId,
              title: request.title,
              analysisType: request.analysisType,
              analystCount: result.analystResponses.length,
              duration: result.duration,
              success: true,
              metadata: {
                userId: request.userId,
                priority: request.priority,
                teamId: AgenticTeamId.BusinessAnalysis,
                modelConfiguration: {
                  primaryModel: result.criteriaModel,
                  fallbackModel: result.optimizationModel,
                  validationModel: result.validationModel
                }
              }
            }
          }
        }
      };

      await this.memoryManager.saveMemory('business-analysis-agent-manager', agentMemory);

      console.log(`PMO business analysis metadata stored for ${request.pmoId}`);
    } catch (error) {
      console.error('Failed to store PMO business analysis metadata:', error);
    }
  }

  /**
   * Store analysis result in Firebase Agent_Output collection for PMO Output tab
   */
  private async storePMOAgentOutput(result: BusinessAnalysisResult, request: PMOBusinessAnalysisRequest): Promise<void> {
    try {
      const { adminDb } = await import('../../../components/firebase-admin');
      const analysisId = `ba-${request.pmoId}-${Date.now()}`;

      // Create consolidated output combining all analyst perspectives
      const consolidatedOutput = `# Business Analysis Report: ${request.title}

## Executive Summary
${result.consolidatedReport || result.consolidatedAnalysis}

## Analysis Details
**Analysis Type:** ${request.analysisType}
**Analysts Involved:** ${result.analystResponses.length}
**Analysis Duration:** ${Math.round(result.duration / 1000)}s
**Completion Date:** ${result.timestamp.toISOString()}

## Individual Analyst Contributions

${result.analystResponses.map(response => `
### ${response.analystName} - ${response.specialty}
**Model Used:** ${response.model}
**Key Findings:**
${response.metadata.keyFindings.map(finding => `- ${finding}`).join('\n')}

**Detailed Analysis:**
${response.response}
`).join('\n---\n')}

## Consolidated Recommendations
${result.consolidatedAnalysis}

---
*Generated by Business Analysis Team - ${new Date().toISOString()}*`;

      // Store as single consolidated document using analysis ID
      const consolidatedOutputData = {
        requestId: analysisId,
        timestamp: new Date(),
        createdAt: new Date(),
        agentType: 'Business Analysis - Consolidated Report',
        userId: this.userId,
        prompt: `${request.title} - Comprehensive Business Analysis`,
        result: {
          thinking: `Multi-analyst business analysis using ${result.analystResponses.length} specialized perspectives`,
          output: consolidatedOutput,
          documentUrl: null
        },
        agentMessages: [],
        modelInfo: {
          provider: 'multi-llm',
          model: 'consolidated-business-analysis'
        },
        contextOptions: {
          customContext: request.description,
          documentReferences: null,
          category: request.documentCategory || null
        },
        metadata: {
          source: 'Business Analysis Team',
          pmoId: request.pmoId,
          recordTitle: request.title,
          recordDescription: request.description,
          recordPriority: request.priority,
          teamId: AgenticTeamId.BusinessAnalysis,
          analysisType: request.analysisType,
          analystCount: result.analystResponses.length,
          duration: result.duration,
          modelConfiguration: result.metadata.modelConfiguration,
          processedAt: new Date().toISOString(),
          autoTriggered: true
        }
      };

      console.log(`[BA_AGENT_OUTPUT] Storing business analysis output with requestId: ${analysisId}`);
      await adminDb.collection('Agent_Output').doc(analysisId).set(consolidatedOutputData);
      console.log(`[BA_AGENT_OUTPUT] Successfully stored business analysis output with requestId: ${analysisId}`);

    } catch (error) {
      console.error(`[BA_AGENT_OUTPUT] Error storing business analysis output:`, error);
      throw error;
    }
  }

  /**
   * Get PMO business analysis history
   */
  async getPMOBusinessAnalysisHistory(pmoId?: string, limit: number = 10): Promise<any[]> {
    try {
      // Load agent memory and extract business analysis results
      const memory = await this.memoryManager.loadMemory('business-analysis-agent-manager');
      const analyses = [];

      if (memory.Agent_Response) {
        for (const [key, value] of Object.entries(memory.Agent_Response)) {
          if (key.startsWith('pmo-business-analysis_') && typeof value === 'object' && value !== null) {
            const analysisData = value as any;
            // Filter by pmoId if provided
            if (!pmoId || analysisData.pmoId === pmoId) {
              analyses.push({
                analysisId: analysisData.analysisId || key,
                pmoId: analysisData.pmoId,
                title: analysisData.title,
                analysisType: analysisData.analysisType,
                analystCount: analysisData.analystCount,
                duration: analysisData.duration,
                timestamp: analysisData.createdAt || analysisData.timestamp,
                success: analysisData.success
              });
            }
          }
        }
      }

      // Sort by timestamp (newest first) and limit results
      analyses.sort((a: any, b: any) => {
        const dateA = new Date(a.timestamp || 0);
        const dateB = new Date(b.timestamp || 0);
        return dateB.getTime() - dateA.getTime();
      });

      return analyses.slice(0, limit);
    } catch (error) {
      console.error('Error retrieving business analysis history:', error);
      return [];
    }
  }

  /**
   * PMO Integration: Create strategic implementation plan as Business Analysis team coordinator
   */
  async createPMOStrategicPlan(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    error?: string;
  }> {
    try {
      console.log(`[BusinessAnalysisAgentManager] Creating strategic implementation plan for PMO project: ${params.projectTitle}`);

      // Create comprehensive business analysis request for strategic planning
      const strategicAnalysisRequest: PMOBusinessAnalysisRequest = {
        pmoId: params.pmoId,
        title: `Strategic Implementation Plan: ${params.projectTitle}`,
        description: `${params.projectDescription}\n\nPMO Assessment:\n${params.pmoAssessment}\n\nTeam Selection Rationale:\n${params.teamSelectionRationale}`,
        analysisType: BusinessAnalysisType.COMPREHENSIVE,
        selectedAnalystIds: [], // Use intelligent selection
        primaryModel: 'claude-sonnet-4-0',
        fallbackModel: 'deepseek-r1-distill-llama-70b',
        validationModel: 'claude-sonnet-4-0',
        userId: this.userId,
        priority: params.priority as 'Low' | 'Medium' | 'High' | 'Critical',
        enableSystemAnalysis: true,
        enableRequirementsEngineering: true,
        enableSpecificationDevelopment: true,
        documentCategory: params.category
      };

      // Conduct comprehensive business analysis for strategic planning
      const analysisResult = await this.conductPMOBusinessAnalysis(strategicAnalysisRequest);

      // Generate strategic implementation plan document
      const strategicPlan = `# Strategic Implementation Plan: ${params.projectTitle}

## Executive Summary
This strategic implementation plan provides comprehensive business analysis and actionable recommendations for ${params.projectTitle}.

## Project Overview
**PMO ID:** ${params.pmoId}
**Priority:** ${params.priority}
**Category:** ${params.category}

${params.projectDescription}

## Business Analysis Summary
${analysisResult.consolidatedReport || analysisResult.consolidatedAnalysis}

## Strategic Implementation Roadmap

### Phase 1: Foundation & Analysis (Weeks 1-2)
- Complete detailed requirements gathering
- Finalize system architecture documentation
- Establish project governance framework
- Set up stakeholder communication channels

### Phase 2: Design & Specification (Weeks 3-4)
- Develop detailed functional specifications
- Create comprehensive use case models
- Design user experience workflows
- Establish testing and validation frameworks

### Phase 3: Implementation Planning (Weeks 5-6)
- Create detailed project timeline
- Allocate resources and assign responsibilities
- Establish risk mitigation strategies
- Set up monitoring and reporting mechanisms

### Phase 4: Execution & Monitoring (Ongoing)
- Execute implementation according to plan
- Monitor progress against established metrics
- Conduct regular stakeholder reviews
- Adjust strategy based on feedback and results

## Risk Assessment & Mitigation
Based on the business analysis, key risks and mitigation strategies have been identified and documented within the comprehensive analysis report.

## Success Metrics
- Requirements completion rate
- Stakeholder satisfaction scores
- Implementation timeline adherence
- Quality assurance metrics
- Business value delivery indicators

## Next Steps
1. Review and approve this strategic implementation plan
2. Initiate Phase 1 activities
3. Establish regular progress review meetings
4. Begin detailed requirements gathering process

---
*Strategic Implementation Plan generated by Business Analysis Team*
*Generated: ${new Date().toISOString()}*
*PMO ID: ${params.pmoId}*`;

      const documentTitle = `Strategic Implementation Plan - ${params.projectTitle} (Business Analysis) - ${params.pmoId}`;

      console.log(`[BusinessAnalysisAgentManager] PMO strategic plan created successfully: ${documentTitle}`);

      return {
        success: true,
        strategicPlan,
        documentTitle,
        documentUrl: `business-analysis/strategic-plan/${params.pmoId}`,
        error: undefined
      };
    } catch (error: any) {
      console.error(`[BusinessAnalysisAgentManager] Failed to create PMO strategic plan:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
